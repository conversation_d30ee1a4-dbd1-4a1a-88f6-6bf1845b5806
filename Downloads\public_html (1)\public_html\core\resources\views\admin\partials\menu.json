{"main": [{"keyword": ["Dashboard", "Home", "Panel", "Admin", "Control center", "Overview", "Main hub", "Management hub", "Administrative hub", "Central hub", "Command center", "Administrator portal", "Centralized interface", "Admin console", "Management dashboard", "Main screen", "Administrative dashboard", "Command dashboard", "Main control panel"], "title": "Dashboard", "icon": "las la-tachometer-alt", "route_name": "admin.dashboard"}, {"title": "Manage Rides", "icon": "las la-car", "menu_active": "admin.rides*", "submenu": [{"keyword": ["New Rides", "Rides management"], "title": "New Rides", "route_name": "admin.rides.new", "menu_active": "admin.rides.new"}, {"keyword": ["Running Rides", "Rides management"], "title": "Running Rides", "route_name": "admin.rides.running", "menu_active": "admin.rides.running"}, {"keyword": ["Completed Rides", "Rides management"], "title": "Completed Rides", "route_name": "admin.rides.completed", "menu_active": "admin.rides.completed"}, {"keyword": ["Cancelled Rides", "Rides management"], "title": "Cancelled Rides", "route_name": "admin.rides.canceled", "menu_active": "admin.rides.canceled"}, {"keyword": ["Courier Rides", "Courier service", "Delivery rides", "Rides management"], "title": "Courier Rides", "route_name": "admin.rides.courier", "menu_active": "admin.rides.courier"}, {"keyword": ["All Rides", "Rides management"], "title": "All Rides", "route_name": "admin.rides.all", "menu_active": "admin.rides.all"}]}, {"title": "System Setup", "icon": "las la-list-alt", "menu_active": ["admin.zone.*", "admin.service.index", "admin.coupon.index", "admin.rider.rule.index", "admin.brand.index"], "submenu": [{"keyword": ["zone", "area"], "title": "Manage Zone", "route_name": "admin.zone.index", "menu_active": "admin.zone.*"}, {"keyword": ["service", "car", "bike", "rent", "taxi"], "title": "Manage Service", "route_name": "admin.service.index", "menu_active": "admin.service.index"}, {"title": "Manage Coupon", "route_name": "admin.coupon.index", "menu_active": "admin.coupon.index"}, {"title": "Manage Brand", "route_name": "admin.brand.index", "menu_active": "admin.brand.index"}, {"title": "Rider Rule", "route_name": "admin.rider.rule.index", "menu_active": "admin.rider.rule.index", "keyword": ["rule", "rider rule"]}]}, {"title": "All Reviews", "icon": "las la-star-half-alt", "route_name": "admin.reviews.all"}, {"title": "Promotional Notify", "icon": "las la-ad", "route_name": "admin.promotional.notification.all"}], "people": [{"title": "Manage Riders", "icon": "las la-user-friends", "counters": ["bannedRiderCount", "emailUnverifiedRiderCount", "mobileUnverifiedRiderCount"], "menu_active": ["admin.rider.active", "admin.rider.banned", "admin.rider.email.unverified", "admin.rider.mobile.unverified", "admin.rider.all", "admin.rider.notification.all"], "submenu": [{"keyword": ["active rider", "Manage rider", "User management", "User control", "User status", "User activity", "User analytics"], "title": "Active Riders", "route_name": "admin.rider.active", "menu_active": "admin.rider.active"}, {"keyword": ["banned rider", "Manage rider", "User management", "Account bans", "User activity"], "title": "Banned Riders", "route_name": "admin.rider.banned", "menu_active": "admin.rider.banned", "counter": "bannedRiderCount"}, {"keyword": ["email unverified rider", "Manage rider", "User verification", "User authentication", "User management"], "title": "Email Unverified", "route_name": "admin.rider.email.unverified", "menu_active": "admin.rider.email.unverified", "counter": "emailUnverifiedRiderCount"}, {"keyword": ["mobile unverified rider", "Manage rider", "User verification", "User authentication", "User management"], "title": "Mobile Unverified", "route_name": "admin.rider.mobile.unverified", "menu_active": "admin.rider.mobile.unverified", "counter": "mobileUnverifiedRiderCount"}, {"keyword": ["all rider rider", "Manage rider", "User management", "User control", "User activity", "User analytics"], "title": "All Riders", "route_name": "admin.rider.all", "menu_active": "admin.rider.all"}, {"keyword": ["send notification rider", "Manage rider", "User notifications", "User management", "User activity"], "title": "Send Notification", "route_name": "admin.rider.notification.all", "menu_active": "admin.rider.notification.all"}]}, {"title": "Manage Drivers", "icon": "las la-users", "counters": ["bannedDriversCount", "emailUnverifiedDriversCount", "mobileUnverifiedDriversCount", "unverifiedDriversCount", "pendingDriversCount"], "menu_active": "admin.driver.*", "submenu": [{"title": "Active Drivers", "route_name": "admin.driver.active", "menu_active": "admin.driver.active"}, {"title": "Banned Drivers", "route_name": "admin.driver.banned", "menu_active": "admin.driver.banned", "counter": "bannedDriversCount"}, {"title": "Email Unverified", "route_name": "admin.driver.email.unverified", "menu_active": "admin.driver.email.unverified", "counter": "emailUnverifiedDriversCount"}, {"title": "Mobile Unverified", "route_name": "admin.driver.mobile.unverified", "menu_active": "admin.driver.mobile.unverified", "counter": "mobileUnverifiedDriversCount"}, {"title": "Document Unverified", "route_name": "admin.driver.unverified", "menu_active": "admin.driver.unverified", "counter": "unverifiedDriversCount"}, {"title": "Document Pending", "route_name": "admin.driver.verify.pending", "menu_active": "admin.driver.verify.pending", "counter": "pendingDriversCount"}, {"title": "Vehicle Unverified", "route_name": "admin.driver.vehicle.unverified", "menu_active": "admin.driver.vehicle.unverified", "counter": "vehicleUnverifiedCount"}, {"title": "Vehicle Verify Pending", "route_name": "admin.driver.vehicle.verify.pending", "menu_active": "admin.driver.vehicle.verify.pending", "counter": "pendingVehicleVerificationCount"}, {"title": "All Drivers", "route_name": "admin.driver.all", "menu_active": "admin.driver.all"}, {"title": "Notification to All", "route_name": "admin.driver.notification.all", "menu_active": "admin.driver.notification.all"}, {"title": "Driver Subscriptions", "route_name": "admin.driver.subscriptions.index", "menu_active": "admin.driver.subscriptions.*"}]}], "finance": [{"title": "Driver Deposits", "icon": "las la-wallet", "counters": ["pendingDepositsCount"], "menu_active": "admin.deposit*", "submenu": [{"keyword": ["Pending Deposits", "Deposits", "Deposit management", "Deposit control", "Deposit status", "Deposit activity"], "title": "Pending Deposits", "route_name": "admin.deposit.pending", "menu_active": "admin.deposit.pending", "counter": "pendingDepositsCount"}, {"keyword": ["Approved Deposits", "Deposits", "Deposit management", "Deposit activity"], "title": "Approved Deposits", "route_name": "admin.deposit.approved", "menu_active": "admin.deposit.approved"}, {"keyword": ["Successful Deposits", "Deposits", "Deposit management", "Deposit activity"], "title": "Successful Deposits", "route_name": "admin.deposit.successful", "menu_active": "admin.deposit.successful"}, {"keyword": ["Rejected Deposits", "Deposits", "Deposit management", "Deposit activity"], "title": "Rejected Deposits", "route_name": "admin.deposit.rejected", "menu_active": "admin.deposit.rejected"}, {"keyword": ["Initiated Deposits", "Deposits", "Deposit management", "Deposit activity"], "title": "Initiated Deposits", "route_name": "admin.deposit.initiated", "menu_active": "admin.deposit.initiated"}, {"keyword": ["All Deposits", "Deposits", "Deposit management", "Deposit control", "Deposit activity"], "title": "All Deposits", "route_name": "admin.deposit.list", "menu_active": "admin.deposit.list"}]}, {"title": "<PERSON>", "icon": "las la-money-check", "counters": ["pendingWithdrawCount"], "menu_active": "admin.withdraw.data*", "submenu": [{"keyword": ["Pending Withdrawals", "<PERSON><PERSON><PERSON><PERSON>", "Withdrawal management", "Withdrawal control", "Withdrawal status", "Withdrawal activity"], "title": "Pending Withdrawals", "route_name": "admin.withdraw.data.pending", "menu_active": "admin.withdraw.data.pending", "counter": "pendingWithdrawCount"}, {"keyword": ["Approved Withdrawals", "<PERSON><PERSON><PERSON><PERSON>", "Withdrawal management", "Withdrawal activity"], "title": "Approved Withdrawals", "route_name": "admin.withdraw.data.approved", "menu_active": "admin.withdraw.data.approved"}, {"keyword": ["Rejected <PERSON>s", "<PERSON><PERSON><PERSON><PERSON>", "Withdrawal management", "Withdrawal activity"], "title": "Rejected <PERSON>s", "route_name": "admin.withdraw.data.rejected", "menu_active": "admin.withdraw.data.rejected"}, {"keyword": ["All Withdrawals", "<PERSON><PERSON><PERSON><PERSON>", "Withdrawal management", "Withdrawal control", "Withdrawal activity"], "title": "All Withdrawals", "route_name": "admin.withdraw.data.all", "menu_active": "admin.withdraw.data.all"}]}, {"title": "Gateways", "icon": "las la-credit-card", "menu_active": ["admin.gateway.*", "admin.withdraw.method.*"], "submenu": [{"title": "Payment Gateway", "route_name": "admin.gateway.automatic.index", "menu_active": "admin.gateway.*", "keyword": ["gateway", "payment gateway", "automatic payment gateway", "gateway", "automatic", "configure payment gateways", "<PERSON><PERSON><PERSON><PERSON>", "amarpay", "authorize", "autorize.net", "btcpay", "btc pay", "binance", "blockchain", "cashmaal", "checkout", "coinbase commerce", "coingate", "coinpayments", "flutterwave", "instamojo", "mercado pago", "mollie", "nmi", "now payments", "nowpayments", "payeer", "paypal", "paystack", "paytm", "perfect money", "perfectmoney", "razorpay", "skrill", "sslcommerz", "stripe", "twocheckout", "two checkout", "2checkout", "manual payment gateway", "bank transfer", "mobile money transfer"]}, {"keyword": ["manual withdrawal", "payout", "payment withdrawal", "withdrawal options", "withdrawal settings", "payout methods", "payout settings", "configure withdrawals", "configure payouts"], "title": "Withdrawals Methods", "icon": "la la-wallet", "route_name": "admin.withdraw.method.index"}]}], "verification": [{"title": "Driver Verification Form", "icon": "las la-truck", "route_name": "admin.verification.driver.form", "menu_active": "admin.verification.driver.form", "keyword": ["driver verification", "driver identity verification", "driver document upload", "driver document"]}, {"title": "Vehicle Verification Form", "icon": "las la-car", "route_name": "admin.vehicle.verification.form", "menu_active": "admin.vehicle.verification.form", "keyword": ["vehicle verification", "vehicle identity verification", "vehicle document upload", "vehicle document"]}], "system report": [{"title": "Rider Report", "icon": "las la-car", "menu_active": "admin.report.rider.*", "submenu": [{"title": "Rider Payment", "route_name": "admin.report.rider.payment", "menu_active": "admin.report.rider.payment", "keyword": ["payment", "payment history", "system payment", "ride payment"]}, {"title": "Login History", "route_name": "admin.report.rider.login.history", "menu_active": "admin.report.rider.login.history", "keyword": ["Login History", "Report", "Login report", "Login history", "Login activity"]}, {"title": "Notification History", "route_name": "admin.report.rider.notification.history", "menu_active": "admin.report.rider.notification.history", "keyword": ["Notification History", "Report", "Notification report", "Notification history", "Notification activity", "email log", "email history", "sms log", "sms history", "push notification log", "push notification history"]}]}, {"title": "Driver Report", "icon": "las la-table", "menu_active": "admin.report.driver.*", "submenu": [{"title": "Transaction History", "route_name": "admin.report.driver.transaction", "menu_active": "admin.report.driver.transaction", "keyword": ["Transaction Log", "Report", "Transaction report", "Transaction history", "Transaction activity", "balance sheet", "balance log", "balance history"]}, {"title": "Commission History", "route_name": "admin.report.driver.commission", "menu_active": "admin.report.driver.commission", "keyword": ["profit log", "commission log", "ride commission", "system profit"]}, {"title": "Login History", "route_name": "admin.report.driver.login.history", "menu_active": "admin.report.driver.login.history", "keyword": ["Login History", "Report", "Login report", "Login history", "Login activity"]}, {"title": "Notification History", "route_name": "admin.report.driver.notification.history", "menu_active": "admin.report.driver.notification.history", "keyword": ["Notification History", "Report", "Notification report", "Notification history", "Notification activity", "email log", "email history", "sms log", "sms history", "push notification log", "push notification history"]}]}], "system utilities": [{"title": "Manage Extension", "icon": "las la-puzzle-piece", "route_name": "admin.extensions.index", "menu_active": "admin.extensions.index", "keyword": ["extensions", "plugins", "addons", "extension settings", "plugin settings", "addon settings", "<PERSON><PERSON>a", "custom captcha", "google captcha", "recaptcha", "re-captcha", "re captcha", "tawk", "tawk.to", "tawk to", "analytics", "google analytics", "facebook comment"]}, {"title": "Manage SEO", "icon": "las la-chart-line", "route_name": "admin.seo", "menu_active": "admin.seo", "keyword": ["SEO management", "search engine optimization", "SEO analysis", "keyword research", "meta tags", "site audit", "backlink management", "content optimization", "SEO strategy", "traffic analysis", "on-page SEO", "off-page SEO", "ranking reports"]}, {"title": "Manage Language", "icon": "las la-language", "route_name": "admin.language.manage", "menu_active": "admin.language.*", "keyword": ["language", "localize", "translation", "translate", "internationalization", "language settings", "localization settings", "translation settings", "configure languages", "configure localization"]}], "admin management": [{"title": "Manage Admins", "icon": "las la-user-shield", "route_name": "admin.staff.index", "menu_active": "admin.staff.*", "keyword": ["admin management", "staff management", "admin users", "admin accounts", "admin control", "admin permissions", "admin roles", "staff control", "user management", "admin settings"]}, {"title": "Manage Roles", "icon": "las la-users-cog", "route_name": "admin.roles.index", "menu_active": "admin.roles.*", "keyword": ["role management", "admin roles", "permissions", "role permissions", "access control", "role settings", "permission settings", "user roles", "admin privileges", "role assignment"]}], "settings": [{"title": "General Settings", "icon": "las la-cog", "route_name": "admin.setting.general", "menu_active": "admin.setting.general", "keyword": ["general", "fundamental", "site information", "site", "website settings", "basic settings", "global settings", "site color", "timezone", "site currency", "pagination", "currency format", "site title", "base color", "secondary color", "paginate"]}, {"title": "Brand Setting", "icon": "las la-copyright", "route_name": "admin.setting.brand", "menu_active": "admin.setting.brand", "keyword": ["branding", "brand setting", "identity", "logo upload", "site branding", "brand identity", "favicon", "website icon", "website favicon", "website logo"]}, {"title": "System Configuration", "icon": "las la-tools", "route_name": "admin.setting.system.configuration", "menu_active": "admin.setting.system.configuration", "keyword": ["basic modules", "control", "modules", "system", "configuration settings", "system control", "force ssl", "force secure password", "email control", "sms control", "verification control", "push notification control", "language control", "mobile verification", "email verification"]}, {"title": "Notification Setting", "icon": "las la-bell", "menu_active": "admin.setting.notification.*", "submenu": [{"title": "Global Template", "route_name": "admin.setting.notification.global.email", "menu_active": "admin.setting.notification.global.*", "keyword": ["email configuration", "email setting", "email template", "global template", "global notification"]}, {"title": "<PERSON>ail <PERSON>ting", "route_name": "admin.setting.notification.email", "menu_active": "admin.setting.notification.email", "keyword": ["email configuration", "email setting", "smtp", "sendgrid", "send grid", "mailjet", "mail jet", "php"]}, {"title": "SMS Setting", "route_name": "admin.setting.notification.sms", "menu_active": "admin.setting.notification.sms", "keyword": ["sms configure", "sms setting", "sms template", "sms broadcast", "nexmo", "clickatell", "infobip", "message bird", "twi<PERSON>", "text magic", "custom api"]}, {"title": "Push Notification Setting", "route_name": "admin.setting.notification.push", "menu_active": "admin.setting.notification.push", "keyword": ["push notification configure", "push notification setting", "firebase setting", "firebase control", "push notification template"]}, {"title": "Notification Templates", "route_name": "admin.setting.notification.templates", "menu_active": "admin.setting.notification.templates", "keyword": ["email template", "sms template", "push notification template", "notification template", "template setting", "global template", "global notification", "custom api"]}]}, {"title": "CRON Job Setting", "icon": "las la-clock", "route_name": "admin.cron.index", "menu_active": "admin.cron.*", "keyword": ["cron job", "scheduled tasks", "automated tasks", "task scheduling", "cron schedule", "cron configuration", "background jobs", "cron execution", "cron timing", "automation", "task automation", "cron logs", "job queue", "system tasks"]}, {"keyword": ["GDPR cookie", "cookie policy", "data privacy", "GDPR settings", "cookie policy settings", "data privacy settings"], "title": "GDPR Cookie", "icon": "las la-cookie-bite", "route_name": "admin.setting.cookie"}, {"keyword": ["custom CSS", "modify styles", "frontend", "styling", "design customization", "CSS settings", "style settings", "frontend customization", "design settings", "customize CSS"], "title": "Custom CSS", "icon": "lab la-css3-alt", "route_name": "admin.setting.custom.css"}, {"keyword": ["Site map", "sitemap", "xml", "sitemap.xml"], "title": "Sitemap XML", "subtitle": "Insert the sitemap XML here to enhance SEO performance.", "icon": "las la-sitemap", "route_name": "admin.setting.sitemap"}, {"keyword": ["Robots", "txt", "robots.txt", "robot.txt"], "title": "Robots txt", "subtitle": "Insert the robots.txt content here to enhance bot web crawlers and instruct them on how to interact with certain areas of the website.", "icon": "las la-robot", "route_name": "admin.setting.robot"}, {"keyword": ["maintenance mode", "system maintenance", "system health", "maintenance settings", "system health settings", "enable maintenance", "disable maintenance", "maintenance configuration"], "title": "Maintenance Mode", "icon": "las la-cogs", "route_name": "admin.maintenance.mode"}], "frontend_manager": [{"title": "Manage Pages", "icon": "las la-list", "route_name": "admin.frontend.manage.pages", "menu_active": ["admin.frontend.manage.pages", "admin.frontend.manage.section", "admin.frontend.manage.pages.seo"], "keyword": ["page management", "frontend pages", "page editor", "page creation", "website pages", "content management", "page customization", "page publishing", "page settings", "dynamic pages"]}, {"title": "Manage Sections", "icon": "la la-html5", "route_name": "admin.frontend.index", "menu_active": ["admin.frontend.index", "admin.frontend.sections"], "keyword": ["section management", "frontend sections", "content sections", "section editor", "section customization", "layout sections", "html sections", "dynamic sections", "section templates", "responsive sections"]}], "other": [{"title": "Support Ticket", "icon": "la la-ticket", "menu_active": "admin.ticket.*", "submenu": [{"title": "Pending Ticket", "route_name": "admin.ticket.pending", "menu_active": "admin.ticket.pending", "counter": "pendingTicketCount", "keyword": ["pending tickets", "ticket management", "support tickets", "customer support", "open tickets", "ticket status", "ticket resolution", "ticket assignment", "ticket prioritization"]}, {"title": "Closed Ticket", "route_name": "admin.ticket.closed", "menu_active": "admin.ticket.closed", "keyword": ["closed tickets", "ticket resolution", "ticket history", "resolved tickets", "support tickets", "customer support", "ticket management", "ticket tracking", "ticket feedback", "ticket archive", "issue resolution", "completed tickets"]}, {"title": "Answered Ticket", "route_name": "admin.ticket.answered", "menu_active": "admin.ticket.answered", "keyword": ["answered tickets", "ticket resolution", "resolved tickets", "customer support", "support tickets", "ticket management", "response time", "ticket history", "customer feedback", "ticket follow-up", "closed tickets", "communication logs"]}, {"title": "All Ticket", "route_name": "admin.ticket.index", "menu_active": "admin.ticket.index", "keyword": ["all tickets", "ticket management", "support tickets", "ticket overview", "ticket status", "customer inquiries", "ticket tracking", "ticket history", "ticket filtering", "ticket categorization", "open tickets", "closed tickets"]}], "counters": ["pendingTicketCount"]}, {"title": "Manage Subscriber", "icon": "las la-inbox", "route_name": "admin.subscriber.index", "menu_active": "admin.subscriber.index", "keyword": ["subscriber management", "manage subscribers", "subscriber list", "email subscribers", "subscriber segmentation", "subscription status", "subscriber details", "newsletter subscribers", "subscriber preferences", "unsubscribe management", "subscriber analytics", "contact management"]}, {"title": "Application Information", "icon": "las la-folder-plus", "route_name": "admin.system.info", "menu_active": "admin.system.info", "keyword": ["subscriber management", "manage subscribers", "subscriber list", "email subscribers", "subscriber segmentation", "subscription status", "subscriber details", "newsletter subscribers", "subscriber preferences", "unsubscribe management", "subscriber analytics", "contact management"]}]}