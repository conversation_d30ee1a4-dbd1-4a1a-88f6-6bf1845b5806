@php
    $services = \App\Models\Service::active()->get();
@endphp
<div class="row gy-3">
    <div class="col-12">
        <div class="form-group">
            <label class="form-label">@lang('Service Type')</label>
            <select class="form-select select2" name="service_id" data-minimum-results-for-search="-1">
                <option value="">@lang('Any')</option>
                @foreach ($services as $service)
                    <option value="{{ $service->id }}" @selected(request()->service_id == $service->id)>{{ __($service->name) }}</option>
                @endforeach
            </select>
        </div>
    </div>
    <div class="col-12">
        <x-admin.other.filter_date />
    </div>
    <div class="col-12">
        <x-admin.other.order_by />
    </div>
    <div class="col-12">
        <x-admin.other.per_page_record />
    </div>
    <div class="col-12">
        <x-admin.other.filter_dropdown_btn />
    </div>
</div>
